version: '3.8'

services:
  grok3api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: grok3api-server
    ports:
      - "50015:50015"
    environment:
      - GROK_SERVER_HOST=0.0.0.0
      - GROK_SERVER_PORT=50015
      - G<PERSON>K_TIMEOUT=${GROK_TIMEOUT:-120}
      - GROK_PROXY=${GROK_PROXY:-}
      - GROK_COOKIES=${GROK_COOKIES:-}
      - DISPLAY=:99
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:50015/v1/models"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - grok3api-network

networks:
  grok3api-network:
    driver: bridge

volumes:
  logs:
  data:
