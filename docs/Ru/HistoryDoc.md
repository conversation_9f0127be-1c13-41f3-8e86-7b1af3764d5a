# 📚 Описание класса `History`

## 🚀 Управляет историей чата с поддержкой изображений, системными промптами и сохранением в JSON

Класс `History` предназначен для управления историей чатов, включая поддержку системных промптов, а также их сохранения и загрузки из файла JSON.

> ❗ **Важно**:  
> Grok может неправильно воспринимать историю. Поэкспериментируйте с `history_as_json`. Всегда можно отключить автоматическое сохранение истории, установив `history_msg_count` на `0` (по умолчанию и так `history_msg_count = 0`).

> ✅ Теперь даже без использования класса History, по умолчанию будет сохраняться история чата на серверах Grok (будут подгружаться старые сообщения). Однако, чаты, созданные на других cookies подгрузить невозможно, что может помешать использовать историю из серверов Grok при автоматическом управлении cookies. История из класса History при использовании истории из сервера будут добавлена только к первому сообщению каждого серверного чата (например при авто-смене cookies создаётся новый серверный чат). 

> 📁 **Сохранение в файл**:  
> История автоматически подгружается из файла при инициализации `GrokClient`, но сохранять необходимо вручную, вызывая `client.history.to_file`.

---

### 🌟 Пример

```python
from grok3api.client import GrokClient


def main():
    # Активируем авто-сохранение истории для 5 сообщений 
    client = GrokClient(history_msg_count=5)

    # Устанавливаем общий системный промпт
    client.history.set_main_system_prompt("Представь что ты баскетболист")
    while True:
        prompt = input("Ведите запрос: ")
        if prompt == "q": break
        result = client.ask(prompt, "0")
        print(result.modelResponse.message)

        # Вручную сохраняем историю в файл
        client.history.to_file()


if __name__ == '__main__':
    main()
```
---

### 📨 **Инициализация**

Класс `History` инициализируется **автоматически при создании GrokClient** со следующими параметрами:

| Параметр            | Тип    | Описание                                                          | По умолчанию            |
|---------------------|--------|-------------------------------------------------------------------|-------------------------|
| `history_msg_count` | `int`  | Максимальное количество сообщений в истории чата                  | `0`                     |
| `history_path`      | `str`  | Путь к файлу JSON для сохранения и загрузки истории               | `"chat_histories.json"` |
| `history_as_json`   | `bool` | Формат вывода истории: JSON (`True`) или строка (`False`)         | `True`                  |
| `history_auto_save` | `bool` | Автоматическая перезапись истории в файл после каждого сообщения. | `True`                  |


---

### 🎯 **Атрибуты**

| Атрибут              | Тип                                                  | Описание                                                                                                                                                                                          |
|----------------------|------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `chat_histories`     | `Dict[str, List[Dict[str, Union[str, List[Dict]]]]]` | Словарь, где ключи — идентификаторы истории (`history_id`), а значения — списки сообщений. Каждое сообщение содержит `role` (тип отправителя) и `content` (список с текстом и/или изображениями). |
| `history_msg_count`  | `int`                                                | Максимальное количество сообщений в истории для каждого `history_id`.                                                                                                                             |
| `system_prompts`     | `Dict[str, str]`                                     | Словарь системных промптов, где ключи — `history_id`, а значения — текстовые промпты для конкретных историй.                                                                                      |
| `main_system_prompt` | `Optional[str]`                                      | Основной системный промпт, применяемый, если для `history_id` не задан специфический промпт.                                                                                                      |
| `history_path`       | `str`                                                | Путь к файлу JSON для хранения истории.                                                                                                                                                           |
| `history_as_json`    | `bool`                                               | Указывает, возвращать ли историю в формате JSON (`True`) или как строку с указанием отправителя (`False`).                                                                                        |

---

### 📜 **Методы**

| Метод                    | Параметры                                                                                       | Возвращает | Описание                                                                                                                                                                                                                                                           |
|--------------------------|-------------------------------------------------------------------------------------------------|------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `set_main_system_prompt` | `text: str`                                                                                     | -          | Устанавливает основной системный промпт, который будет использоваться по умолчанию. При возникновении ошибки логирует исключение через `logger.error`.                                                                                                             |
| `set_system_prompt`      | `history_id: str`, `text: str`                                                                  | -          | Устанавливает системный промпт для конкретного идентификатора истории. Ошибки логируются через `logger.error`.                                                                                                                                                     |
| `get_system_prompt`      | `history_id: str`                                                                               | `str`      | Возвращает системный промпт для указанного идентификатора или пустую строку, если промпт не установлен. Ошибки логируются через `logger.error`.                                                                                                                    |
| `to_file`                | -                                                                                               | -          | Сохраняет текущие данные (`chat_histories`, `system_prompts`, `main_system_prompt`) в файл JSON. Данные записываются с отступами и без принудительного использования ASCII. Ошибки логируются через `logger.error`.                                                |

---

> 💡 **Примечание**:  
> Если `history_msg_count = 0`, история будет содержать только системный промпт (при его наличии).
