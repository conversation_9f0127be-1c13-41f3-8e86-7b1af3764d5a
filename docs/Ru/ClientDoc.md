# 🛠️ Описание класса `GrokClient`

## 🚀 Основной класс для взаимодействия с Grok API.

Класс `GrokClient` — это основной инструмент для работы с Grok, который используется для отправки запросов к модели и автоматического сохранения истории.

> 📁 **Работа с историей**:  
> При инициализации объекта класса `GrokClient` автоматически инициализируется объект класса `History`. История автоматически подгружается из файла при инициализации `GrokClient`.


### `GrokClient` принимает при инициализации:

| Параметр                  | Тип                                 | Описание                                                                                                                             | По умолчанию            |  
|---------------------------|-------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------|-------------------------|
| `cookies`                 | `str` / `dict` / `List[str / dict]` | Cookie сайта grok.com (Не обязательно)                                                                                               | `None`                  |
| `use_xvfb`                | `bool`                              | Флаг для использования Xvfb на Linux.                                                                                                | `True`                  |
| `proxy`                   | `str`                               | URL Прокси сервера, используется только в случае региональной блокировки.                                                            | `-`                     |  
| `history_msg_count`       | `int`                               | Количество сообщений в истории.                                                                                                      | `0` (история отключена) |  
| `history_path`            | `str`                               | Путь к файлу с историей в JSON-формате.                                                                                              | `"chat_histories.json"` |  
| `history_as_json`         | `bool`                              | Отправлять ли историю в Grok в формате JSON (если > 0).                                                                              | `True`                  |
| `always_new_conversation` | `bool`                              | Использовать ли url создания нового чата при отправке запроса к Grok.                                                                | `True`                  |  
| `conversation_id`         | `str`                               | ID чата grok.com Если хотите продолжить беседу с того места где остановились. Только в паре с response_id.                           | `None`                  |  
| `response_id`             | `str`                               | ID ответа Grok в чате conversation_id. Если хотите продолжить беседу с того места где остановились. Только в паре с conversation_id. | `None`                  |
| `enable_artifact_files`   | `bool`                              | Если `False`, то html-объявления файлов будут заменены на markdown-стиль с ` ```{lang} `.                                            | `False`                 |  
| `timeout`                 | `int`                               | Максимальное время на инициализацию клиента (в секундах).                                                                            | `120`                   |  

---

### 🎯 **Возвращает:**  
- Экземпляр класса `GrokClient`, готовый к использованию.

---



### 📋 **Дополнительная информация**

- 🌐 **Автоматическая инициализация браузера**: При инициализации клиента, сессия Chrome будет запущена автоматически, чтобы подготовить все к отправке запросов.
- 🍪 **Автоматическая смена cookie**: если передан лист cookies (лист строк или словарей), то, в случае достижения лимита сообщений, произойдёт автоматическая смена cookie — новый порядок будет сохранён для текущего и последующих запросов.
- 🐧 **Поддержка Linux**: [Подробное описание работы на Linux](LinuxDoc.md)

> 💡  На Linux без GUI нужно использовать Xvfb для стабильной работы в headless-режиме.

> 🛠️ Для начала работы с Grok API создайте экземпляр `GrokClient` и используйте его методы, например, `ChatCompletion.create`, для отправки запросов.

---

### 🌟 **Пример использования**

```python
from grok3api.client import GrokClient


def main():
    # Можно добавить лист строк / словарей для автоматической смены в случае достижения лимита
    # cookies = "YOUR_COOKIES_FROM_BROWSER"
    # client = GrokClient(cookies=cookies)
    
    # Создание клиента (cookies будут автоматически получены, если не переданы)
    client = GrokClient()

    # Отправляем запрос через ChatCompletion
    response = client.ask(message="Привет, Grok!")
    print(response.modelResponse.message)  # Выводит ответ от Grok


if __name__ == '__main__':
    main()
```

---

### 🔗 **Связанные объекты**

- **`ChatCompletion`**: Объект, создаваемый внутри `GrokClient`, предоставляет метод `create` для отправки запросов к модели Grok. Подробности смотрите в **[Описании метода `create`](askDoc.md)**.

---

### 📌 **Примечания**

- **Обработка ошибок**: При инициализации класса возможны исключения (например, если куки не удалось получить). Они логируются через `logger.error`.
