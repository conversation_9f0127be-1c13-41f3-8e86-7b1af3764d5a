# Описания метода `ask`


## 🚀 Отправляет запрос к API Grok и получает ответ. Есть асинхронный вариант `async_ask`.

### 📨 **Принимает:**  
- 📜`message`: Текст запроса для модели.  
- ⚙ `**kwargs`: Дополнительные параметры для настройки.  

### 🎯 **Возвращает:**  
- `GrokResponse` – объект с ответом от API Grok.
- **[Описание `GrokResponse`](GrokResponse.md)**


### Полный список параметров для `ask`:

| Параметр                | Тип                                                                   | Описание                                                                                                                                                                         | По умолчанию |
|-------------------------|-----------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------|
| `message`               | `str`                                                                 | **Обязательный**. Текст запроса для Grok.                                                                                                                                        | -            |
| `history_id`            | `str`                                                                 | Идентификатор в словаре историй (если используется авто сохранение истории)                                                                                                      | `None`       |
| `new_conversation`      | `bool`                                                                | Использовать ли url нового чата при отправке запроса в Grok (не касается встроенного класса History).                                                                            | `False`      |
| `timeout`               | `int`                                                                 | Таймаут одного ожидания получения ответа (секунды).                                                                                                                              | `45`         |
| `temporary`             | `bool`                                                                | Указывает, временный ли запрос или сессия.                                                                                                                                       | `False`      |
| `modelName`             | `str`                                                                 | Название модели AI (например, "grok-3").                                                                                                                                         | `"grok-3"`   |
| `images`                | `str_path` / `str_base64` / `BytesIO` / `List[str]` / `List[BytesIO]` | Или путь к изображению, или base64-кодированное изображение, или BytesIO (можно список любого из перечисленных типов) для отправки. Не должно быть использовано fileAttachments. | `None`       |
| `fileAttachments`       | `List[str]`                                                           | Список файлов-вложений (ключи: `name`, `content`).                                                                                                                               | `[]`         |
| `imageAttachments`      | `List[]`                                                              | Список изображений-вложений (ключи: `name`, `content`).                                                                                                                          | `[]`         |
| `customInstructions`    | `str`                                                                 | Дополнительные инструкции для модели.                                                                                                                                            | `""`         |
| `disableSearch`         | `bool`                                                                | Отключить поиск в ответах модели.                                                                                                                                                | `False`      |
| `enableImageGeneration` | `bool`                                                                | Включить генерацию изображений.                                                                                                                                                  | `True`       |
| `enableImageStreaming`  | `bool`                                                                | Включить потоковую передачу изображений.                                                                                                                                         | `True`       |
| `enableSideBySide`      | `bool`                                                                | Включить отображение информации бок о бок.                                                                                                                                       | `True`       |
| `imageGenerationCount`  | `int`                                                                 | Количество генерируемых изображений.                                                                                                                                             | `4`          |
| `isPreset`              | `bool`                                                                | Указывает, предустановленное ли сообщение.                                                                                                                                       | `False`      |
| `isReasoning`           | `bool`                                                                | Включить режим рассуждений модели.                                                                                                                                               | `False`      |
| `returnImageBytes`      | `bool`                                                                | Возвращать изображения в виде байтов.                                                                                                                                            | `False`      |
| `toolOverrides`         | `Dict[str, Any]`                                                      | Переопределение настроек инструментов.                                                                                                                                           | `{}`         |


new_conversation (Optional[bool]): Использовать ли url нового чата при отправке запроса в Grok (не касается встроенного класса History).

> 💡 Важно понимать, что эти параметры получены путем реверс-инженеринга браузерных запросов. И, возможно, некоторые из них пока могут не иметь функционала, особенно учитывая, свежесть модели `Grok3`

> ❗ Описание тех параметров, функционал которых не удалось подтвердить в тестировании, составлены на основе похожих параметров в официальной документации xAI API. 

> 🛠️ Вы можете внести свой вклад, просто экспериментируя с различными параметрами!