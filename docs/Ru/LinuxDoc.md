

## 🐧 **Особенности работы с Linux**


### 🌟 **Основные требования**

Для работы `GrokClient` в **Linux** необходимо наличие установленного **Google Chrome**. Если используется серверная версия Linux (например, **Ubuntu Server**) без графического интерфейса, потребуется установить **Xvfb** для эмуляции виртуального экрана, что обеспечит работу Chrome в headless режиме. ✨

---

### 🛠️ **Установка Google Chrome на Linux**

Для установки **Google Chrome** откройте терминал и выполните следующую команду (пример):

```bash
wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
```

```bash
sudo apt update
sudo apt install -y google-chrome-stable
```

---

### 🎥 **Установка Xvfb для headless режима**

На системах без графического интерфейса установка **Xvfb** позволяет создать виртуальный экран. Для установки выполните:

```bash
sudo apt update
sudo apt install -y xvfb
```

> 🌟 **Примечание:** Xvfb создаёт виртуальный экран с минимальными характеристиками, что позволяет запускать Chrome без необходимости наличия физического дисплея.

---

### ⚙️ **Параметры использования Xvfb**

При создании экземпляра `GrokClient` доступны следующие параметры для настройки работы с Xvfb:

| Параметр          | Тип    | Описание                                                                      | Значение по умолчанию |
|-------------------|--------|-------------------------------------------------------------------------------|-----------------------|
| `use_xvfb`        | `bool` | Флаг, определяющий использование Xvfb в Linux.                                | `True`                |

> ❗ **Важно:** На Linux по умолчанию используется `use_xvfb=True`. При наличии графического интерфейса данную опцию рекомендуется отключить.

---

### 🌟 **Пример: Отключение Xvfb при наличии графического интерфейса**

При наличии графического интерфейса можно создать экземпляр клиента следующим образом:

```python
from grok3api.client import GrokClient

client = GrokClient(use_xvfb=False)
```

> 💡 В данном случае приложение будет использовать реальный графический интерфейс.

---

### 📌 **Итог**

- **Xvfb** используется для эмуляции графического экрана на системах без GUI.
- По умолчанию `use_xvfb=True`; при наличии графического интерфейса данную опцию следует отключить.
