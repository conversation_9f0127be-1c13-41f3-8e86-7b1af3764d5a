## 📦 Changelog


---

### 🆕 v0.1.0b1

#### ✨ Новое:

* 🛠 **Улучшена обработка код-блоков**
  Добавлена автоматическая трансформация вложенных блоков `<xaiArtifact contentType="text/...">...</xaiArtifact>` в стандартные Markdown-блоки с указанием языка. Также поддерживается преобразование нестандартных заголовков код-блоков:

  * ` ```x-<lang>src` → ` ```<lang>`
  * ` ```x-<lang>` → ` ```<lang>`

  Это улучшает отображение кода, независимо от исходного формата.

* ☑️ Функцию можно отключить, указав параметр `auto_transform_code_blocks=False` при создании `GrokClient`.

---


### 🆕 v0.0.9b1

#### ✨ Новое:
- 💬 **Поддержка продолжения существующих бесед с Grok (Решение [issue](https://github.com/boykopovar/Grok3API/issues/4))**  
  Теперь `client.ask` может автоматически продолжать уже начатую беседу, используя `conversation_id` и `response_id`. Эти параметры получаются автоматически при отправке первого сообщения, но можно задать свои при создании клиента.  
  Если они получены и валидны, запрос отправляется в текущую беседу, а не создаётся новый чат, что в какой-то мере автоматизирует управление историей и повышает отзывчивость.

- ➕ **Добавлены новые параметры для управления беседами**  
  - `always_new_conversation` — параметр клиента: позволяет всегда начинать новый чат независимо от предыдущих сообщений.
  - `new_conversation` — параметр метода `ask`: позволяет начать новый чат вручную при отправке запроса (не влияет на сохранённую в классе History историю). История из класса History при использовании истории с сервера будет добавлена только к первому сообщению каждого серверного чата (например, при авто-смене cookies создаётся новый серверный чат).

- 🆙 **Расширен объект `GrokResponse`**  
  Теперь дополнительно возвращаются данные о беседе (ниже только новые поля):
  - `conversationId` — идентификатор чата.
  - `title` — заголовок чата, если был сгенерирован или обновлён.
  - `conversationCreateTime` — время создания чата.
  - `conversationModifyTime` — время последнего изменения чата.
  - `temporary` — флаг временного чата (`True` — временный, `False` — постоянный, `None` — неизвестно).



#### 📋 Примечания:
- ✅ Теперь даже без использования встроенной истории (`History`) чаты сохраняются на серверах Grok и подгружаются при запросах.
- ⚠️ Чаты, созданные с другими cookies, не могут быть загружены — это ограничение серверной стороны (при автоматической смене cookies создаётся новый серверный чат).

#### 📚 Подробнее
- [💼️ Описание класса `GrokClient`](ClientDoc.md)
- [✈️ Описание метода `ask`](askDoc.md)
- [📋 Описание класса `History`](HistoryDoc.md)
- [📬 Описание класса `GrokResponse`](GrokResponse.md)

---



### 🆕 v0.0.1b11

#### ✨ Новое:
- 🖼️ **Поддержка отправки изображений в Grok**  
  Теперь стало намного проще отправлять изображения на сервер Grok!

```python
from grok3api.client import GrokClient


def main():
    client = GrokClient()
    result = client.ask(
        message="Что на картинке?",
        images="C:\\photo1_to_grok.jpg"
    )
    print(f"Ответ Grok3: {result.modelResponse.message}")

if __name__ == '__main__':
    main()
```

Подробное описание метода отправки доступно [здесь](askDoc.md).

- 🤖 **Продолжается работа над OpenAI-совместимостью**  
  - ✅ Теперь поддерживаются **любые `api_key`** для работы с сервером.
  - ⚙️ Добавлена возможность настройки сервера (через аргументы командной строки и переменные окружения). Подробная инструкция по запуску доступна в [🌐 Запуск OpenAI-совместимого сервера](OpenAI_Server.md).

> ⚠️ **Сервер всё ещё находится на ранней стадии, некоторые функции могут быть нестабильными.**

---

### 🆕 v0.0.1b10

#### ✨ Новое:
- 🔐 **Автоматическое получение cookies**  
  Теперь больше **не обязательно указывать cookies вручную** — клиент сам их получит при необходимости.  
  ➕ Однако, вы **всё ещё можете указать свои cookies**, если хотите использовать свой аккаунт (например, для генерации с кастомными настройками или доступом к платным возможностям).

- 🤖 **Начало работы над OpenAI-совместимостью ([**server.py**](../../grok3api/server.py), [**Demo**](../../tests/openai_test.py))**  
  Реализован первый черновик сервера, совместимого с API OpenAI. Пока **работает "как-то"**, но:
  > ⚠️ **Находится на ранней стадии и не отлажен. Используйте на свой страх и риск!**

---