# Grok3API Docker 快速部署

## 🚀 一键部署

```bash
# 1. 启动服务
docker compose up -d

# 2. 测试 API
python3 test_api.py

# 3. 使用示例客户端
python3 example_client.py
```

## 📡 API 端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/v1/models` | GET | 列出可用模型 |
| `/v1/chat/completions` | POST | OpenAI 兼容的聊天接口 |
| `/v1/string` | GET/POST | 简单文本查询 |

## 🔧 配置

编辑 `.env` 文件：

```bash
GROK_SERVER_PORT=50015
GROK_TIMEOUT=120
GROK_PROXY=          # 可选：代理服务器
GROK_COOKIES=        # 可选：预设 cookies
```

## 💻 使用示例

### Python OpenAI 客户端

```python
from openai import OpenAI

client = OpenAI(
    base_url="http://127.0.0.1:50015/v1",
    api_key="dummy"
)

response = client.chat.completions.create(
    model="grok-3",
    messages=[{"role": "user", "content": "Hello!"}]
)

print(response.choices[0].message.content)
```

### curl 命令

```bash
# 获取模型列表
curl http://127.0.0.1:50015/v1/models

# 聊天对话
curl -X POST http://127.0.0.1:50015/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "grok-3",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

## 🛠️ 管理命令

```bash
# 查看状态
docker compose ps

# 查看日志
docker compose logs -f grok3api

# 重启服务
docker compose restart grok3api

# 停止服务
docker compose down
```

## 📋 故障排除

1. **端口被占用**: 修改 `.env` 中的 `GROK_SERVER_PORT`
2. **服务启动失败**: 检查 `docker compose logs grok3api`
3. **Chrome 相关错误**: 确保 Docker 有足够内存
4. **API 响应慢**: 增加 `GROK_TIMEOUT` 值

## 📚 更多信息

- 详细部署指南: [DOCKER_DEPLOYMENT.md](DOCKER_DEPLOYMENT.md)
- 项目文档: [README.md](README.md)
