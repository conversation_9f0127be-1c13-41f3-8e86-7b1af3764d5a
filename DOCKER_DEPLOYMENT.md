# Grok3API Docker 部署指南

这个项目提供了一个 Docker Compose 部署方案，用于运行 Grok3API 服务器，提供 OpenAI 兼容的 API 接口。

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone <your-repo-url>
cd Grok3API
```

### 2. 配置环境变量
编辑 `.env` 文件，根据需要调整配置：

```bash
# Grok3API 服务器配置
GROK_SERVER_HOST=0.0.0.0
GROK_SERVER_PORT=50015

# Grok API 配置
GROK_TIMEOUT=120
GROK_PROXY=
GROK_COOKIES=
```

### 3. 启动服务
```bash
docker compose up -d
```

### 4. 验证服务
```bash
# 检查服务状态
docker compose ps

# 查看日志
docker compose logs -f grok3api

# 测试 API
curl http://127.0.0.1:50015/v1/models
```

## 📡 API 端点

### 1. 列出可用模型
```bash
GET http://127.0.0.1:50015/v1/models
```

响应示例：
```json
{
  "object": "list",
  "data": [
    {
      "id": "grok-3",
      "object": "model",
      "created": 1703123456,
      "owned_by": "xai"
    },
    {
      "id": "grok-2",
      "object": "model", 
      "created": 1703123456,
      "owned_by": "xai"
    }
  ]
}
```

### 2. 聊天完成 (OpenAI 兼容)
```bash
POST http://127.0.0.1:50015/v1/chat/completions
Content-Type: application/json

{
  "model": "grok-3",
  "messages": [
    {
      "role": "user",
      "content": "Hello, how are you?"
    }
  ]
}
```

### 3. 简单文本查询
```bash
# GET 请求
GET http://127.0.0.1:50015/v1/string?q=Hello

# POST 请求
POST http://127.0.0.1:50015/v1/string
Content-Type: text/plain

Hello, Grok!
```

## 🔧 配置说明

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `GROK_SERVER_HOST` | 服务器绑定地址 | `0.0.0.0` |
| `GROK_SERVER_PORT` | 服务器端口 | `50015` |
| `GROK_TIMEOUT` | 请求超时时间（秒） | `120` |
| `GROK_PROXY` | 代理服务器地址 | 空 |
| `GROK_COOKIES` | 预设的 cookies | 空 |

### 数据持久化

- `./logs` - 日志文件
- `./data` - 数据文件

## 🐛 故障排除

### 1. 检查容器状态
```bash
docker compose ps
docker compose logs grok3api
```

### 2. 重启服务
```bash
docker compose restart grok3api
```

### 3. 完全重建
```bash
docker compose down
docker compose build --no-cache
docker compose up -d
```

### 4. 常见问题

**问题：Chrome 浏览器相关错误**
- 确保容器有足够的内存
- 检查 Docker 资源限制

**问题：Cookie 获取失败**
- 检查网络连接
- 考虑配置代理服务器

**问题：API 响应慢**
- 增加 `GROK_TIMEOUT` 值
- 检查网络延迟

## 📝 使用示例

### Python 客户端示例
```python
from openai import OpenAI

client = OpenAI(
    base_url="http://127.0.0.1:50015/v1",
    api_key="dummy"  # 任意值，服务器不验证
)

response = client.chat.completions.create(
    model="grok-3",
    messages=[
        {"role": "user", "content": "What is Python?"}
    ]
)

print(response.choices[0].message.content)
```

### curl 示例
```bash
curl -X POST http://127.0.0.1:50015/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "grok-3",
    "messages": [
      {"role": "user", "content": "Hello!"}
    ]
  }'
```

## 🔒 安全注意事项

1. 不要在生产环境中暴露端口到公网
2. 考虑使用反向代理（如 nginx）
3. 定期更新镜像和依赖
4. 监控资源使用情况

## 📊 监控和日志

### 健康检查
服务包含内置的健康检查，每 30 秒检查一次 `/v1/models` 端点。

### 日志查看
```bash
# 实时日志
docker compose logs -f grok3api

# 最近的日志
docker compose logs --tail=100 grok3api
```

## 🛠️ 开发模式

如果需要开发模式，可以挂载源代码：

```yaml
# 在 docker-compose.yml 中添加
volumes:
  - .:/app
  - ./logs:/app/logs
  - ./data:/app/data
```

然后重启服务：
```bash
docker compose down
docker compose up -d
```
