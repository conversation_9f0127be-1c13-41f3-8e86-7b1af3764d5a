#!/bin/bash

# Grok3API Docker 部署脚本
# 用于快速设置和部署 Grok3API 服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    print_success "依赖检查通过"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_warning "端口 $port 已被占用"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 创建必要的目录
create_directories() {
    print_info "创建必要的目录..."
    mkdir -p logs data
    print_success "目录创建完成"
}

# 构建和启动服务
deploy_service() {
    print_info "构建 Docker 镜像..."
    docker compose build
    
    print_info "启动服务..."
    docker compose up -d
    
    print_success "服务启动完成"
}

# 等待服务就绪
wait_for_service() {
    print_info "等待服务就绪..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://127.0.0.1:50015/v1/models > /dev/null 2>&1; then
            print_success "服务已就绪"
            return 0
        fi
        
        echo -n "."
        sleep 2
        ((attempt++))
    done
    
    print_error "服务启动超时"
    return 1
}

# 运行测试
run_tests() {
    print_info "运行 API 测试..."
    
    if [ -f "test_api.py" ]; then
        python3 test_api.py
    else
        print_warning "测试脚本不存在，跳过测试"
    fi
}

# 显示服务信息
show_service_info() {
    echo
    print_success "🎉 Grok3API 服务部署完成！"
    echo
    echo "📡 API 端点:"
    echo "  - 模型列表: http://127.0.0.1:50015/v1/models"
    echo "  - 聊天完成: http://127.0.0.1:50015/v1/chat/completions"
    echo "  - 简单查询: http://127.0.0.1:50015/v1/string"
    echo
    echo "🔧 管理命令:"
    echo "  - 查看日志: docker compose logs -f grok3api"
    echo "  - 重启服务: docker compose restart grok3api"
    echo "  - 停止服务: docker compose down"
    echo "  - 查看状态: docker compose ps"
    echo
    echo "📚 更多信息请查看 DOCKER_DEPLOYMENT.md"
}

# 主函数
main() {
    echo "🚀 Grok3API Docker 部署脚本"
    echo "================================"
    
    # 检查依赖
    check_dependencies
    
    # 检查端口
    check_port 50015
    
    # 创建目录
    create_directories
    
    # 部署服务
    deploy_service
    
    # 等待服务就绪
    if wait_for_service; then
        # 运行测试
        run_tests
        
        # 显示服务信息
        show_service_info
    else
        print_error "服务部署失败"
        print_info "查看日志: docker compose logs grok3api"
        exit 1
    fi
}

# 处理命令行参数
case "${1:-}" in
    "test")
        print_info "仅运行测试..."
        python3 test_api.py
        ;;
    "stop")
        print_info "停止服务..."
        docker compose down
        print_success "服务已停止"
        ;;
    "restart")
        print_info "重启服务..."
        docker compose restart grok3api
        print_success "服务已重启"
        ;;
    "logs")
        print_info "显示日志..."
        docker compose logs -f grok3api
        ;;
    "status")
        print_info "服务状态:"
        docker compose ps
        ;;
    *)
        main
        ;;
esac
