#!/usr/bin/env python3
"""
Grok3API OpenAI 兼容客户端示例
演示如何使用 OpenAI Python 库连接到 Grok3API 服务器
"""

from openai import OpenAI
import json
import time


def main():
    # 创建 OpenAI 客户端，指向本地 Grok3API 服务器
    client = OpenAI(
        base_url="http://127.0.0.1:50015/v1",
        api_key="dummy"  # Grok3API 不验证 API key，可以使用任意值
    )
    
    print("🤖 Grok3API OpenAI 兼容客户端示例")
    print("=" * 50)
    
    # 1. 列出可用模型
    print("\n📋 获取可用模型...")
    try:
        models = client.models.list()
        print(f"✅ 找到 {len(models.data)} 个模型:")
        for model in models.data:
            print(f"   - {model.id} (owned by: {model.owned_by})")
    except Exception as e:
        print(f"❌ 获取模型失败: {e}")
        return
    
    # 2. 简单对话
    print("\n💬 开始对话...")
    try:
        response = client.chat.completions.create(
            model="grok-3",
            messages=[
                {"role": "user", "content": "Hello! Can you tell me a joke?"}
            ]
        )
        
        print(f"🤖 Grok 回复: {response.choices[0].message.content}")
        print(f"📊 Token 使用: {response.usage}")
        
    except Exception as e:
        print(f"❌ 对话失败: {e}")
        return
    
    # 3. 多轮对话
    print("\n🔄 多轮对话示例...")
    try:
        messages = [
            {"role": "user", "content": "What is Python programming language?"},
        ]
        
        response = client.chat.completions.create(
            model="grok-3",
            messages=messages
        )
        
        # 添加助手回复到对话历史
        messages.append({
            "role": "assistant", 
            "content": response.choices[0].message.content
        })
        
        print(f"🤖 第一轮回复: {response.choices[0].message.content[:100]}...")
        
        # 继续对话
        messages.append({
            "role": "user", 
            "content": "Can you give me a simple Python example?"
        })
        
        response = client.chat.completions.create(
            model="grok-3",
            messages=messages
        )
        
        print(f"🤖 第二轮回复: {response.choices[0].message.content[:100]}...")
        
    except Exception as e:
        print(f"❌ 多轮对话失败: {e}")
        return
    
    # 4. 不同模型测试
    print("\n🔬 测试不同模型...")
    for model_name in ["grok-3", "grok-2"]:
        try:
            start_time = time.time()
            response = client.chat.completions.create(
                model=model_name,
                messages=[
                    {"role": "user", "content": f"Hello from {model_name}!"}
                ]
            )
            end_time = time.time()
            
            print(f"✅ {model_name}: {response.choices[0].message.content[:50]}... "
                  f"(耗时: {end_time - start_time:.2f}s)")
            
        except Exception as e:
            print(f"❌ {model_name} 测试失败: {e}")
    
    print("\n🎉 示例完成！")


def interactive_chat():
    """交互式聊天模式"""
    client = OpenAI(
        base_url="http://127.0.0.1:50015/v1",
        api_key="dummy"
    )
    
    print("🤖 Grok3API 交互式聊天")
    print("输入 'quit' 或 'exit' 退出")
    print("=" * 40)
    
    messages = []
    
    while True:
        try:
            user_input = input("\n👤 您: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            
            if not user_input:
                continue
            
            messages.append({"role": "user", "content": user_input})
            
            print("🤖 Grok 正在思考...")
            response = client.chat.completions.create(
                model="grok-3",
                messages=messages
            )
            
            assistant_reply = response.choices[0].message.content
            messages.append({"role": "assistant", "content": assistant_reply})
            
            print(f"🤖 Grok: {assistant_reply}")
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "chat":
        interactive_chat()
    else:
        main()
