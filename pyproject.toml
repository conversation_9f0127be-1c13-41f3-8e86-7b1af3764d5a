[build-system]
requires = ["setuptools>=61.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "Grok3API"
version = "0.1.0b6"
description = "Python-библиотека для взаимодействия с Grok3, ориентированная на максимальную простоту использования. Автоматически получает cookies, поэтому ничего не нужно указывать. A Python library for interacting with Grok3, focused on maximum ease of use. Automatically gets cookies, so you don't have to specify anything."
authors = [{ name = "boykopovar", email = "<EMAIL>" }]
license = "MIT"
readme = "README.md"
requires-python = ">=3.8"
dependencies = ["undetected-chromedriver>=3.5.5"]
keywords = ["grok3api", "grok 3 api", "grok api", "grok3api python", "grok ai", "unofficial grok3api api"]
classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent",
]

[project.urls]
Homepage = "https://github.com/boykopovar/Grok3API"

[tool.setuptools]
include-package-data = true

[tool.setuptools.packages.find]
where = ["."]
include = ["grok3api*"]