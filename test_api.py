#!/usr/bin/env python3
"""
Grok3API 测试脚本
用于验证 Docker 部署的 API 服务是否正常工作
"""

import requests
import json
import time
from typing import Dict, Any


class Grok3APITester:
    def __init__(self, base_url: str = "http://127.0.0.1:50015"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def test_health(self) -> bool:
        """测试服务健康状态"""
        try:
            response = self.session.get(f"{self.base_url}/v1/models", timeout=10)
            return response.status_code == 200
        except Exception as e:
            print(f"健康检查失败: {e}")
            return False
    
    def test_models_endpoint(self) -> Dict[str, Any]:
        """测试模型列表端点"""
        print("🔍 测试 /v1/models 端点...")
        try:
            response = self.session.get(f"{self.base_url}/v1/models")
            response.raise_for_status()
            data = response.json()
            
            print(f"✅ 状态码: {response.status_code}")
            print(f"📋 可用模型数量: {len(data.get('data', []))}")
            
            for model in data.get('data', []):
                print(f"   - {model.get('id')} (owned by: {model.get('owned_by')})")
            
            return {"success": True, "data": data}
        except Exception as e:
            print(f"❌ 模型端点测试失败: {e}")
            return {"success": False, "error": str(e)}
    
    def test_chat_completions(self, message: str = "Hello, how are you?") -> Dict[str, Any]:
        """测试聊天完成端点"""
        print(f"💬 测试 /v1/chat/completions 端点...")
        print(f"📝 发送消息: {message}")
        
        try:
            payload = {
                "model": "grok-3",
                "messages": [
                    {"role": "user", "content": message}
                ]
            }
            
            start_time = time.time()
            response = self.session.post(
                f"{self.base_url}/v1/chat/completions",
                json=payload,
                timeout=60
            )
            end_time = time.time()
            
            response.raise_for_status()
            data = response.json()
            
            print(f"✅ 状态码: {response.status_code}")
            print(f"⏱️ 响应时间: {end_time - start_time:.2f} 秒")
            print(f"🤖 回复: {data['choices'][0]['message']['content'][:100]}...")
            print(f"📊 Token 使用: {data.get('usage', {})}")
            
            return {"success": True, "data": data, "response_time": end_time - start_time}
        except Exception as e:
            print(f"❌ 聊天端点测试失败: {e}")
            return {"success": False, "error": str(e)}
    
    def test_string_endpoint_get(self, query: str = "Hello") -> Dict[str, Any]:
        """测试字符串端点 (GET)"""
        print(f"📝 测试 /v1/string 端点 (GET)...")
        print(f"🔍 查询: {query}")
        
        try:
            response = self.session.get(
                f"{self.base_url}/v1/string",
                params={"q": query},
                timeout=30
            )
            response.raise_for_status()
            
            print(f"✅ 状态码: {response.status_code}")
            print(f"📄 响应: {response.text[:100]}...")
            
            return {"success": True, "response": response.text}
        except Exception as e:
            print(f"❌ 字符串端点 (GET) 测试失败: {e}")
            return {"success": False, "error": str(e)}
    
    def test_string_endpoint_post(self, message: str = "Tell me a joke") -> Dict[str, Any]:
        """测试字符串端点 (POST)"""
        print(f"📝 测试 /v1/string 端点 (POST)...")
        print(f"💭 消息: {message}")
        
        try:
            response = self.session.post(
                f"{self.base_url}/v1/string",
                data=message,
                headers={"Content-Type": "text/plain"},
                timeout=30
            )
            response.raise_for_status()
            
            print(f"✅ 状态码: {response.status_code}")
            print(f"📄 响应: {response.text[:100]}...")
            
            return {"success": True, "response": response.text}
        except Exception as e:
            print(f"❌ 字符串端点 (POST) 测试失败: {e}")
            return {"success": False, "error": str(e)}
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        print("🚀 开始 Grok3API 测试...")
        print("=" * 50)
        
        results = {}
        
        # 健康检查
        print("🏥 检查服务健康状态...")
        if not self.test_health():
            print("❌ 服务不可用，请检查 Docker 容器状态")
            return {"success": False, "error": "Service unavailable"}
        print("✅ 服务健康状态正常")
        print()
        
        # 测试各个端点
        results["models"] = self.test_models_endpoint()
        print()
        
        results["chat_completions"] = self.test_chat_completions()
        print()
        
        results["string_get"] = self.test_string_endpoint_get()
        print()
        
        results["string_post"] = self.test_string_endpoint_post()
        print()
        
        # 汇总结果
        success_count = sum(1 for result in results.values() if result.get("success"))
        total_count = len(results)
        
        print("=" * 50)
        print(f"📊 测试完成: {success_count}/{total_count} 个测试通过")
        
        if success_count == total_count:
            print("🎉 所有测试通过！API 服务运行正常")
        else:
            print("⚠️ 部分测试失败，请检查日志")
        
        return {
            "success": success_count == total_count,
            "results": results,
            "summary": f"{success_count}/{total_count} tests passed"
        }


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Grok3API 测试工具")
    parser.add_argument(
        "--url", 
        default="http://127.0.0.1:50015",
        help="API 服务器地址 (默认: http://127.0.0.1:50015)"
    )
    parser.add_argument(
        "--message",
        default="Hello, how are you?",
        help="测试聊天消息 (默认: Hello, how are you?)"
    )
    
    args = parser.parse_args()
    
    tester = Grok3APITester(args.url)
    results = tester.run_all_tests()
    
    # 返回适当的退出码
    exit(0 if results["success"] else 1)


if __name__ == "__main__":
    main()
