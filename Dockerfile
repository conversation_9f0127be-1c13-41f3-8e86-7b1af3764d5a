# 使用官方 Python 3.11 镜像作为基础镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    unzip \
    curl \
    xvfb \
    && rm -rf /var/lib/apt/lists/*

# 安装 Google Chrome
RUN wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google-chrome.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

# 首先复制依赖文件
COPY requirements.txt pyproject.toml ./

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir -e .

# 复制项目文件
COPY . .

# 创建非 root 用户
RUN useradd -m -u 1000 grokuser && chown -R grokuser:grokuser /app
USER grokuser

# 暴露端口
EXPOSE 50015

# 设置环境变量
ENV PYTHONPATH=/app
ENV DISPLAY=:99
ENV GROK_SERVER_HOST=0.0.0.0
ENV GROK_SERVER_PORT=50015

# 启动命令
CMD ["python", "-m", "grok3api.server", "--host", "0.0.0.0", "--port", "50015"]
